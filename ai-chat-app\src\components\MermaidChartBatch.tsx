'use client';

import { useEffect, useRef, useState } from 'react';

interface MermaidChartBatchProps {
  charts: string[];
  className?: string;
}

/**
 * Alternative Mermaid component for rendering multiple diagrams
 * Uses mermaid.run() with suppressErrors for better batch processing
 * Based on Context7 best practices
 */
export default function MermaidChartBatch({ charts, className = '' }: MermaidChartBatchProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !containerRef.current || charts.length === 0) {
      setIsLoading(false);
      return;
    }

    let isCancelled = false;

    const renderCharts = async () => {
      if (isCancelled) return;
      
      setIsLoading(true);
      setError(null);

      try {
        // Import mermaid dynamically
        const mermaid = (await import('mermaid')).default;

        if (isCancelled) return;

        // Initialize mermaid with suppressErrors for batch processing
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'inherit'
        });

        if (!containerRef.current || isCancelled) return;

        // Clear container
        containerRef.current.innerHTML = '';

        // Create elements for each chart
        charts.forEach((chart, index) => {
          if (isCancelled || !containerRef.current) return;
          
          const chartDiv = document.createElement('div');
          chartDiv.className = 'mermaid mb-4';
          chartDiv.textContent = chart;
          chartDiv.id = `mermaid-batch-${index}-${Date.now()}`;
          containerRef.current.appendChild(chartDiv);
        });

        // Use mermaid.run with suppressErrors for batch processing
        await mermaid.run({
          suppressErrors: true,
          nodes: containerRef.current.querySelectorAll('.mermaid')
        });

        // Apply responsive styling with height restriction to all SVGs
        if (containerRef.current && !isCancelled) {
          const svgElements = containerRef.current.querySelectorAll('svg');
          svgElements.forEach(svg => {
            svg.style.maxWidth = '100%';
            svg.style.height = 'auto';
            svg.style.maxHeight = '500px';
          });

          // Set container scrolling if any diagram is tall
          containerRef.current.style.maxHeight = '500px';
          containerRef.current.style.overflowY = 'auto';
          containerRef.current.style.overflowX = 'auto';
        }

        if (!isCancelled) {
          setIsLoading(false);
        }
      } catch (err) {
        if (!isCancelled) {
          console.error('Mermaid batch rendering error:', err);
          setError(err instanceof Error ? err.message : 'Failed to render diagrams');
          setIsLoading(false);
        }
      }
    };

    const timer = setTimeout(renderCharts, 100);
    
    return () => {
      isCancelled = true;
      clearTimeout(timer);
    };
  }, [charts, mounted]);

  if (!mounted) return null;

  if (error) {
    return (
      <div className={`p-4 border border-red-200 rounded-lg bg-red-50 ${className}`}>
        <div className="flex items-center gap-2 text-red-700 mb-2">
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span className="font-medium">Batch Diagram Error</span>
        </div>
        <p className="text-sm text-red-600 mb-2">{error}</p>
        <details className="mt-2">
          <summary className="text-sm text-red-600 cursor-pointer hover:text-red-800">
            Show diagram sources
          </summary>
          <div className="mt-2 space-y-2">
            {charts.map((chart, index) => (
              <pre key={index} className="p-2 bg-red-100 rounded text-xs text-red-800 overflow-x-auto">
                Chart {index + 1}:{'\n'}{chart}
              </pre>
            ))}
          </div>
        </details>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`p-4 border border-gray-200 rounded-lg bg-gray-50 ${className}`}>
        <div className="flex items-center justify-center gap-2 text-gray-600">
          <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
          <span className="text-sm">Rendering {charts.length} diagram{charts.length > 1 ? 's' : ''}...</span>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`mermaid-batch-chart p-4 border border-gray-200 rounded-lg bg-white ${className}`}
      style={{
        maxHeight: '500px',
        overflowX: 'auto',
        overflowY: 'auto'
      }}
    />
  );
}
