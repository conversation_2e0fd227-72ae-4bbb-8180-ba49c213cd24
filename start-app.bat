@echo off
echo Starting AI Chat Application...
echo.

echo Checking Docker PostgreSQL container...
docker ps | findstr postgres
if %errorlevel% neq 0 (
    echo PostgreSQL container not found. Please start it manually:
    echo docker-compose -f ai-chat-app/docker-compose.db.yml up -d
    pause
    exit /b 1
)

echo.
echo Starting Python service (Enhanced Mode)...
start "Python Service" cmd /k "cd make-it-heavy-service && python start.py"

echo.
echo Waiting for Python service to start...
timeout /t 5 /nobreak > nul

echo.
echo Starting Next.js application...
start "Next.js App" cmd /k "cd ai-chat-app && npm run dev"

echo.
echo Services starting...
echo - PostgreSQL Database: Docker container (port 5432)
echo - Python Service: http://localhost:8000
echo - Next.js App: http://localhost:3000 (or next available port)
echo.
echo Press any key to exit...
pause > nul
