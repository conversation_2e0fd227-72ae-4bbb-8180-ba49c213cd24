'use client';

import { useEffect, useRef, useState } from 'react';

interface SecureMermaidChartProps {
  chart: string;
  className?: string;
  allowedDiagramTypes?: string[];
}

/**
 * Secure Mermaid component with enhanced security settings
 * Based on Context7 security best practices
 */
export default function SecureMermaidChart({ 
  chart, 
  className = '',
  allowedDiagramTypes = ['graph', 'flowchart', 'sequenceDiagram', 'classDiagram', 'stateDiagram', 'pie', 'gantt']
}: SecureMermaidChartProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Security validation
  const validateDiagramSecurity = (diagramText: string): { isValid: boolean; error?: string } => {
    // Check for allowed diagram types
    const trimmedChart = diagramText.trim();
    const firstLine = trimmedChart.split('\n')[0].toLowerCase();
    
    const isAllowedType = allowedDiagramTypes.some(type => 
      firstLine.includes(type.toLowerCase()) || 
      trimmedChart.toLowerCase().startsWith(type.toLowerCase())
    );

    if (!isAllowedType) {
      return {
        isValid: false,
        error: `Diagram type not allowed. Allowed types: ${allowedDiagramTypes.join(', ')}`
      };
    }

    // Check for potentially dangerous content
    const dangerousPatterns = [
      /<script/i,
      /javascript:/i,
      /onclick/i,
      /onerror/i,
      /onload/i,
      /<iframe/i,
      /<object/i,
      /<embed/i
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(diagramText)) {
        return {
          isValid: false,
          error: 'Diagram contains potentially unsafe content'
        };
      }
    }

    return { isValid: true };
  };

  useEffect(() => {
    if (!mounted || !elementRef.current || !chart.trim()) {
      setIsLoading(false);
      return;
    }

    let isCancelled = false;

    const renderChart = async () => {
      if (isCancelled) return;
      
      setIsLoading(true);
      setError(null);

      // Security validation
      const validation = validateDiagramSecurity(chart);
      if (!validation.isValid) {
        setError(validation.error || 'Security validation failed');
        setIsLoading(false);
        return;
      }

      try {
        // Import mermaid dynamically
        const mermaid = (await import('mermaid')).default;

        if (isCancelled) return;

        // Initialize mermaid with strict security settings
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'strict', // Enhanced security
          fontFamily: 'inherit',
          // Additional security configurations
          htmlLabels: false, // Disable HTML labels for security
          flowchart: {
            htmlLabels: false
          },
          sequence: {
            actorMargin: 50,
            showSequenceNumbers: false
          }
        });

        if (!elementRef.current || isCancelled) return;

        // Generate unique ID for this render
        const uniqueId = `secure-mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // Use the modern mermaid.render API
        const { svg, bindFunctions } = await mermaid.render(uniqueId, chart);

        if (isCancelled || !elementRef.current) return;

        // Additional SVG sanitization could be added here if needed
        // For now, we trust Mermaid's built-in security with strict mode

        // Clear and set the SVG content
        elementRef.current.innerHTML = svg;

        // Bind any interactive functions if they exist (with strict security, this should be minimal)
        if (bindFunctions) {
          bindFunctions(elementRef.current);
        }

        // Apply responsive styling
        const svgElement = elementRef.current.querySelector('svg');
        if (svgElement) {
          svgElement.style.maxWidth = '100%';
          svgElement.style.height = 'auto';
        }

        if (!isCancelled) {
          setIsLoading(false);
        }
      } catch (err) {
        if (!isCancelled) {
          console.error('Secure Mermaid rendering error:', err);
          setError(err instanceof Error ? err.message : 'Failed to render diagram securely');
          setIsLoading(false);
        }
      }
    };

    const timer = setTimeout(renderChart, 100);
    
    return () => {
      isCancelled = true;
      clearTimeout(timer);
    };
  }, [chart, mounted, allowedDiagramTypes]);

  if (!mounted) return null;

  if (error) {
    return (
      <div className={`p-4 border border-red-200 rounded-lg bg-red-50 ${className}`}>
        <div className="flex items-center gap-2 text-red-700 mb-2">
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span className="font-medium">Secure Diagram Error</span>
        </div>
        <p className="text-sm text-red-600 mb-2">{error}</p>
        <div className="text-xs text-red-500">
          Allowed diagram types: {allowedDiagramTypes.join(', ')}
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`p-4 border border-gray-200 rounded-lg bg-gray-50 ${className}`}>
        <div className="flex items-center justify-center gap-2 text-gray-600">
          <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
          <span className="text-sm">Securely rendering diagram...</span>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={elementRef}
      className={`secure-mermaid-chart p-4 border border-gray-200 rounded-lg bg-white overflow-x-auto ${className}`}
    />
  );
}
