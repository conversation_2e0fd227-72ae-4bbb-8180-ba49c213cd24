#!/bin/bash

echo "Starting AI Chat Application..."
echo

echo "Checking Docker PostgreSQL container..."
if ! docker ps | grep -q postgres; then
    echo "PostgreSQL container not found. Please start it manually:"
    echo "docker-compose -f ai-chat-app/docker-compose.db.yml up -d"
    exit 1
fi

echo
echo "Starting Python service (Enhanced Mode)..."
cd make-it-heavy-service
python start.py &
PYTHON_PID=$!
cd ..

echo
echo "Waiting for Python service to start..."
sleep 5

echo
echo "Starting Next.js application..."
cd ai-chat-app
npm run dev &
NEXTJS_PID=$!
cd ..

echo
echo "Services started:"
echo "- PostgreSQL Database: Docker container (port 5432)"
echo "- Python Service: http://localhost:8000 (PID: $PYTHON_PID)"
echo "- Next.js App: http://localhost:3000 (PID: $NEXTJS_PID)"
echo
echo "Press Ctrl+C to stop all services..."

# Wait for interrupt
trap 'echo "Stopping services..."; kill $PYTHON_PID $NEXTJS_PID; exit' INT
wait
