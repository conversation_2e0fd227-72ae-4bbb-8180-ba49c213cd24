# AI Chat Application

A comprehensive AI chat application built with Next.js, featuring enhanced mode with Python agents, mermaid chart rendering, file management, and persistent database storage.

## Features

- 🤖 **AI-Powered Chat**: Integrates with OpenAI-compatible APIs (using Chutes AI)
- 🔧 **Enhanced Mode**: Python-based agents with advanced capabilities via make-it-heavy service
- 💬 **Multiple Conversations**: Create, manage, and switch between multiple chat sessions
- 💾 **Database Storage**: PostgreSQL database for persistent conversation storage
- 📊 **Mermaid Charts**: Render diagrams and flowcharts directly in chat messages
- 📁 **File Management**: Built-in file manager for agent-created files
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile devices
- 🎨 **Modern UI**: Clean, ChatGPT-inspired interface with dark sidebar
- ⚡ **Real-time Streaming**: Messages stream in real-time as they're generated
- ✏️ **Conversation Management**: Rename, delete, and organize your chats
- 🧠 **Memory Integration**: Optional Zep memory layer for enhanced context

## Prerequisites

- Node.js 18+
- Docker and Docker Compose
- Python 3.8+ (for enhanced mode service)
- npm, yarn, or pnpm

## Quick Start

### 1. Start the Database (PostgreSQL in Docker)

The application uses PostgreSQL running in Docker. Make sure Docker is running, then check if the database container is already running:

```bash
docker ps
```

If you see a container named `nexus-db` or similar PostgreSQL container, it's already running. If not, you may need to start it:

```bash
# If you have a docker-compose file for the database
docker-compose -f docker-compose.db.yml up -d
```

**Important**: This application is configured to use the Docker PostgreSQL instance, not any Windows-installed PostgreSQL. The Docker container should be running on port 5432.

### 2. Install Node.js Dependencies

```bash
cd ai-chat-app
npm install
```

### 3. Configure Environment Variables

The `.env.local` file should already be configured. Verify it contains:

```env
# API Configuration
NEXT_PUBLIC_CHUTES_API_TOKEN=your_chutes_api_token_here

# Python Service (Enhanced Mode)
PYTHON_SERVICE_URL=http://localhost:8000

# Database Configuration (Docker PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ai_chat_app
DB_USER=postgres
DB_PASSWORD=nexus123

# Optional: Zep Memory Service
ZEP_API_URL=http://localhost:8001
ZEP_API_KEY=your-local-zep-secret-key
```

### 4. Start the Python Service (Enhanced Mode)

For enhanced chat capabilities, start the Python service:

```bash
cd ../make-it-heavy-service
python start.py
```

This will start the service on `http://localhost:8000`.

### 5. Start the Next.js Application

```bash
cd ai-chat-app
npm run dev
```

The application will be available at `http://localhost:3000` (or the next available port if 3000 is occupied).

### Alternative: Use Startup Scripts

For convenience, you can use the provided startup scripts:

**Windows:**
```bash
start-app.bat
```

**Linux/Mac:**
```bash
chmod +x start-app.sh
./start-app.sh
```

These scripts will automatically start both the Python service and Next.js application in separate terminal windows.

## Service Architecture

The application consists of three main services:

1. **Next.js Frontend** (Port 3000+) - Main web application
2. **PostgreSQL Database** (Port 5432) - Data persistence (Docker container)
3. **Python Service** (Port 8000) - Enhanced mode agents and tools

## Troubleshooting

### Database Connection Issues

If you see "Failed to fetch settings: Internal Server Error":

1. **Check Docker PostgreSQL is running**:
   ```bash
   docker ps
   ```
   Look for a PostgreSQL container (e.g., `nexus-db`).

2. **Verify database connection**:
   ```bash
   docker exec <container-name> psql -U postgres -l
   ```
   Should show `ai_chat_app` database.

3. **Check for port conflicts**:
   - Windows PostgreSQL might be using port 5432
   - Make sure Docker PostgreSQL is accessible on port 5432
   - The app is configured to use the Docker instance

### Python Service Issues

If enhanced mode doesn't work:

1. **Check Python service is running**:
   ```bash
   curl http://localhost:8000/health
   ```

2. **Start the service manually**:
   ```bash
   cd make-it-heavy-service
   python start.py
   ```

### Port Conflicts

- Next.js will automatically use the next available port (3001, 3002, etc.)
- Check the terminal output for the actual port being used
- Update any hardcoded URLs if necessary

## Features Guide

### Mermaid Chart Rendering

You can create diagrams in chat messages using mermaid syntax:

````
```mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
```
````

### File Management

- Access the file manager via the file icon in the header
- View, download, and delete files created by enhanced mode agents
- Navigate back to chat using the back button

### Enhanced Mode

- Toggle enhanced mode for access to Python-based agents
- Requires the Python service to be running
- Provides advanced capabilities like web search, calculations, and file operations

## Project Structure

```
src/
├── app/                 # Next.js app directory
├── components/          # React components
│   ├── Sidebar.tsx     # Navigation sidebar
│   ├── ChatArea.tsx    # Main chat display
│   ├── ChatInput.tsx   # Message input component
│   └── MessageBubble.tsx # Individual message display
├── hooks/              # Custom React hooks
│   └── useChat.ts      # Main chat state management
├── lib/                # Utility libraries
│   ├── api.ts          # API service for LLM communication
│   ├── storage.ts      # Local storage utilities
│   └── utils.ts        # General utilities
└── types/              # TypeScript type definitions
    └── chat.ts         # Chat-related types
```

## Usage

1. **Start a New Chat**: Click the "New Chat" button in the sidebar
2. **Send Messages**: Type your message and press Enter (Shift+Enter for new lines)
3. **Manage Conversations**:
   - Click on any conversation in the sidebar to switch to it
   - Hover over conversations to see rename and delete options
   - Conversations are automatically titled based on the first message
4. **Mobile**: Use the hamburger menu to access the sidebar on mobile devices

## Technologies Used

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety and better development experience
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful SVG icons
- **Mermaid.js** - Diagram and flowchart rendering

### Backend
- **PostgreSQL** - Database for persistent storage (Docker)
- **Python FastAPI** - Enhanced mode service with agents
- **Docker** - Containerized database deployment

### Optional Services
- **Zep** - Memory layer for enhanced context (Docker)

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
