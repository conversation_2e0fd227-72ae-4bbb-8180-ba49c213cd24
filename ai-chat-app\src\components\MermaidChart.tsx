'use client';

import { useEffect, useRef, useState } from 'react';

interface MermaidChartProps {
  chart: string;
  className?: string;
}

export default function MermaidChart({ chart, className = '' }: MermaidChartProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !elementRef.current || !chart.trim()) {
      setIsLoading(false);
      return;
    }

    let isCancelled = false;

    const renderChart = async () => {
      if (isCancelled) return;

      setIsLoading(true);
      setError(null);

      try {
        // Import mermaid dynamically
        const mermaid = (await import('mermaid')).default;

        if (isCancelled) return;

        // Initialize mermaid with recommended config
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'inherit'
        });

        if (!elementRef.current || isCancelled) return;

        // Generate unique ID for this render
        const uniqueId = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // Use the modern mermaid.render API
        const { svg, bindFunctions } = await mermaid.render(uniqueId, chart);

        if (isCancelled || !elementRef.current) return;

        // Clear and set the SVG content
        elementRef.current.innerHTML = svg;

        // Bind any interactive functions if they exist
        if (bindFunctions) {
          bindFunctions(elementRef.current);
        }

        // Apply responsive styling with height restriction
        const svgElement = elementRef.current.querySelector('svg');
        if (svgElement) {
          svgElement.style.maxWidth = '100%';
          svgElement.style.height = 'auto';
          svgElement.style.maxHeight = '500px';

          // Get the original dimensions
          const originalWidth = svgElement.getAttribute('width');
          const originalHeight = svgElement.getAttribute('height');

          if (originalHeight && originalWidth) {
            const heightNum = parseFloat(originalHeight);
            const widthNum = parseFloat(originalWidth);

            // If height exceeds 500px, we need to enable scrolling
            if (heightNum > 500) {
              // Set the container to allow scrolling
              if (elementRef.current) {
                elementRef.current.style.maxHeight = '500px';
                elementRef.current.style.overflowY = 'auto';
                elementRef.current.style.overflowX = 'auto';
              }
            }
          }
        }

        if (!isCancelled) {
          setIsLoading(false);
        }
      } catch (err) {
        if (!isCancelled) {
          console.error('Mermaid rendering error:', err);
          setError(err instanceof Error ? err.message : 'Failed to render diagram');
          setIsLoading(false);
        }
      }
    };

    // Small delay to ensure DOM is ready
    const timer = setTimeout(renderChart, 100);

    return () => {
      isCancelled = true;
      clearTimeout(timer);
    };
  }, [chart, mounted]);

  if (error) {
    return (
      <div className={`p-4 border border-red-200 rounded-lg bg-red-50 ${className}`}>
        <div className="flex items-center gap-2 text-red-700 mb-2">
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span className="font-medium">Diagram Error</span>
        </div>
        <p className="text-sm text-red-600">{error}</p>
        <details className="mt-2">
          <summary className="text-sm text-red-600 cursor-pointer hover:text-red-800">
            Show diagram source
          </summary>
          <pre className="mt-2 p-2 bg-red-100 rounded text-xs text-red-800 overflow-x-auto">
            {chart}
          </pre>
        </details>
      </div>
    );
  }

  // Don't render anything on server side
  if (!mounted) {
    return null;
  }

  if (isLoading) {
    return (
      <div className={`p-4 border border-gray-200 rounded-lg bg-gray-50 ${className}`}>
        <div className="flex items-center justify-center gap-2 text-gray-600">
          <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          <span className="text-sm">Rendering diagram...</span>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={elementRef}
      className={`mermaid-chart p-4 border border-gray-200 rounded-lg bg-white ${className}`}
      style={{
        minHeight: '100px',
        maxHeight: '500px',
        overflowX: 'auto',
        overflowY: 'auto',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    />
  );
}
