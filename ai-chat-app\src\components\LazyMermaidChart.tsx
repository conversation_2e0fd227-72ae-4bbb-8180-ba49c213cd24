'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

interface LazyMermaidChartProps {
  chart: string;
  className?: string;
  threshold?: number; // Intersection observer threshold
  rootMargin?: string; // Intersection observer root margin
}

/**
 * Lazy-loaded Mermaid component that only renders when visible
 * Based on Context7 performance best practices
 */
export default function LazyMermaidChart({ 
  chart, 
  className = '',
  threshold = 0.1,
  rootMargin = '50px'
}: LazyMermaidChartProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [hasRendered, setHasRendered] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Intersection Observer setup
  const setupObserver = useCallback(() => {
    if (!elementRef.current || !mounted) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !hasRendered) {
          setIsVisible(true);
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    observerRef.current.observe(elementRef.current);
  }, [threshold, rootMargin, hasRendered, mounted]);

  // Cleanup observer
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  // Setup observer when component mounts
  useEffect(() => {
    if (mounted && elementRef.current) {
      setupObserver();
    }
  }, [mounted, setupObserver]);

  // Render chart when visible
  useEffect(() => {
    if (!isVisible || hasRendered || !chart.trim()) return;

    let isCancelled = false;

    const renderChart = async () => {
      if (isCancelled) return;
      
      setIsLoading(true);
      setError(null);

      try {
        // Import mermaid dynamically only when needed
        const mermaid = (await import('mermaid')).default;

        if (isCancelled) return;

        // Initialize mermaid with optimized settings
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'inherit',
          // Performance optimizations
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true
          },
          sequence: {
            useMaxWidth: true
          }
        });

        if (!elementRef.current || isCancelled) return;

        // Generate unique ID for this render
        const uniqueId = `lazy-mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // Use the modern mermaid.render API
        const { svg, bindFunctions } = await mermaid.render(uniqueId, chart);

        if (isCancelled || !elementRef.current) return;

        // Clear and set the SVG content
        elementRef.current.innerHTML = svg;

        // Bind any interactive functions if they exist
        if (bindFunctions) {
          bindFunctions(elementRef.current);
        }

        // Apply responsive styling
        const svgElement = elementRef.current.querySelector('svg');
        if (svgElement) {
          svgElement.style.maxWidth = '100%';
          svgElement.style.height = 'auto';
        }

        if (!isCancelled) {
          setIsLoading(false);
          setHasRendered(true);
          
          // Disconnect observer after successful render
          if (observerRef.current) {
            observerRef.current.disconnect();
          }
        }
      } catch (err) {
        if (!isCancelled) {
          console.error('Lazy Mermaid rendering error:', err);
          setError(err instanceof Error ? err.message : 'Failed to render diagram');
          setIsLoading(false);
        }
      }
    };

    const timer = setTimeout(renderChart, 100);
    
    return () => {
      isCancelled = true;
      clearTimeout(timer);
    };
  }, [isVisible, hasRendered, chart]);

  if (!mounted) return null;

  if (error) {
    return (
      <div className={`p-4 border border-red-200 rounded-lg bg-red-50 ${className}`}>
        <div className="flex items-center gap-2 text-red-700 mb-2">
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span className="font-medium">Lazy Diagram Error</span>
        </div>
        <p className="text-sm text-red-600 mb-2">{error}</p>
        <details className="mt-2">
          <summary className="text-sm text-red-600 cursor-pointer hover:text-red-800">
            Show diagram source
          </summary>
          <pre className="mt-2 p-2 bg-red-100 rounded text-xs text-red-800 overflow-x-auto">
            {chart}
          </pre>
        </details>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`p-4 border border-gray-200 rounded-lg bg-gray-50 ${className}`}>
        <div className="flex items-center justify-center gap-2 text-gray-600">
          <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
          <span className="text-sm">Loading diagram...</span>
        </div>
      </div>
    );
  }

  if (!isVisible && !hasRendered) {
    return (
      <div 
        ref={elementRef}
        className={`p-4 border border-gray-200 rounded-lg bg-gray-50 ${className}`}
        style={{ minHeight: '100px' }}
      >
        <div className="flex items-center justify-center gap-2 text-gray-500">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <span className="text-sm">Diagram will load when visible</span>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={elementRef}
      className={`lazy-mermaid-chart p-4 border border-gray-200 rounded-lg bg-white overflow-x-auto ${className}`}
      style={{ 
        minHeight: hasRendered ? 'auto' : '100px'
      }}
    />
  );
}
