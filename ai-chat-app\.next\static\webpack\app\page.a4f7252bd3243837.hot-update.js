"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Sidebar(param) {\n    let { conversations, currentConversationId, isOpen, onToggle, onNewChat, onSelectConversation, onDeleteConversation, onRenameConversation } = param;\n    _s();\n    const [editingId, setEditingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTitle, setEditTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hoveredId, setHoveredId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleStartEdit = (conversation)=>{\n        setEditingId(conversation.id);\n        setEditTitle(conversation.title);\n    };\n    const handleSaveEdit = ()=>{\n        if (editingId && editTitle.trim()) {\n            onRenameConversation(editingId, editTitle.trim());\n        }\n        setEditingId(null);\n        setEditTitle('');\n    };\n    const handleCancelEdit = ()=>{\n        setEditingId(null);\n        setEditTitle('');\n    };\n    const formatDate = (timestamp)=>{\n        const date = new Date(timestamp);\n        const now = new Date();\n        const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n        if (diffInDays === 0) {\n            return 'Today';\n        } else if (diffInDays === 1) {\n            return 'Yesterday';\n        } else if (diffInDays < 7) {\n            return \"\".concat(diffInDays, \" days ago\");\n        } else {\n            return date.toLocaleDateString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-0 top-0 h-full bg-sidebar text-sidebar-foreground z-50 transition-transform duration-300 ease-in-out\", \"w-80 md:w-64\", isOpen ? \"translate-x-0\" : \"-translate-x-full\", \"md:relative md:translate-x-0\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"Nexus\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    className: \"p-2 hover:bg-sidebar-accent rounded-lg md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onNewChat,\n                                className: \"w-full flex items-center gap-3 p-3 bg-sidebar-accent hover:bg-sidebar-accent/80 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"New Chat\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto px-4 pb-4\",\n                            children: conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sidebar-muted text-center py-8\",\n                                children: \"No conversations yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group relative p-3 rounded-lg cursor-pointer transition-colors\", currentConversationId === conversation.id ? \"bg-sidebar-accent\" : \"hover:bg-sidebar-accent/50\"),\n                                        onClick: ()=>onSelectConversation(conversation.id),\n                                        onMouseEnter: ()=>setHoveredId(conversation.id),\n                                        onMouseLeave: ()=>setHoveredId(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: editingId === conversation.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: editTitle,\n                                                        onChange: (e)=>setEditTitle(e.target.value),\n                                                        onBlur: handleSaveEdit,\n                                                        onKeyDown: (e)=>{\n                                                            if (e.key === 'Enter') handleSaveEdit();\n                                                            if (e.key === 'Escape') handleCancelEdit();\n                                                        },\n                                                        className: \"w-full bg-sidebar-accent text-sidebar-foreground px-2 py-1 rounded text-sm\",\n                                                        autoFocus: true,\n                                                        onClick: (e)=>e.stopPropagation()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-sm truncate\",\n                                                                children: conversation.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-sidebar-muted mt-1\",\n                                                                children: formatDate(conversation.updatedAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                handleStartEdit(conversation);\n                                                            },\n                                                            className: \"p-1 hover:bg-sidebar-accent rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                onDeleteConversation(conversation.id);\n                                                            },\n                                                            className: \"p-1 hover:bg-sidebar-accent rounded text-red-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, conversation.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"fixed top-4 left-4 z-30 p-2 bg-sidebar text-sidebar-foreground rounded-lg md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"qm4EDoor6TX324RUHSr/Gh7kHFs=\");\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});