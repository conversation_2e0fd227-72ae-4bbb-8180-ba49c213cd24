/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/enhanced-chat/route";
exports.ids = ["app/api/enhanced-chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhanced-chat%2Froute&page=%2Fapi%2Fenhanced-chat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhanced-chat%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhanced-chat%2Froute&page=%2Fapi%2Fenhanced-chat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhanced-chat%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_USER_Dev_AI_Chat_chat11_ai_chat_app_src_app_api_enhanced_chat_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/enhanced-chat/route.ts */ \"(rsc)/./src/app/api/enhanced-chat/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/enhanced-chat/route\",\n        pathname: \"/api/enhanced-chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/enhanced-chat/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\app\\\\api\\\\enhanced-chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_USER_Dev_AI_Chat_chat11_ai_chat_app_src_app_api_enhanced_chat_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/enhanced-chat/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhanced-chat%2Froute&page=%2Fapi%2Fenhanced-chat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhanced-chat%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/enhanced-chat/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/enhanced-chat/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(rsc)/./src/lib/api.ts\");\n/* harmony import */ var _lib_service_discovery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/service-discovery */ \"(rsc)/./src/lib/service-discovery.ts\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// Function to get user settings from file\nfunction getUserSettings() {\n    try {\n        const settingsPath = path__WEBPACK_IMPORTED_MODULE_4___default().join(process.cwd(), 'user-settings.json');\n        if (fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(settingsPath)) {\n            const settingsData = fs__WEBPACK_IMPORTED_MODULE_3___default().readFileSync(settingsPath, 'utf8');\n            return JSON.parse(settingsData);\n        }\n    } catch (error) {\n        console.warn('Failed to load user settings:', error);\n    }\n    return null;\n}\nasync function POST(request) {\n    let messages;\n    let mode = 'single';\n    let stream = true;\n    let config = {};\n    try {\n        const body = await request.json();\n        ({ messages, mode = 'single', stream = true, config = {} } = body);\n        console.log(`Enhanced chat request: mode=${mode}, messages=${messages?.length || 0}, stream=${stream}, tools_enabled=${config.tools_enabled}`);\n        // Validate request\n        if (!messages || !Array.isArray(messages) || messages.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid messages format'\n            }, {\n                status: 400\n            });\n        }\n        // Discover the correct service URL\n        const PYTHON_SERVICE_URL = await (0,_lib_service_discovery__WEBPACK_IMPORTED_MODULE_2__.discoverServiceUrl)();\n        // Determine endpoint based on mode\n        const endpoint = mode === 'orchestrator' ? '/chat/orchestrator' : '/chat/single';\n        const serviceUrl = `${PYTHON_SERVICE_URL}${endpoint}`;\n        // Check if Python service is available (it should be since we just discovered it)\n        let serviceAvailable = false;\n        try {\n            console.log(`Checking Python service health at: ${PYTHON_SERVICE_URL}/health`);\n            const healthCheck = await fetch(`${PYTHON_SERVICE_URL}/health`, {\n                method: 'GET',\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            serviceAvailable = healthCheck.ok;\n            console.log(`Python service health check: ${serviceAvailable ? 'OK' : 'FAILED'}`);\n            // If health check fails, clear the cached URL to force rediscovery next time\n            if (!serviceAvailable) {\n                (0,_lib_service_discovery__WEBPACK_IMPORTED_MODULE_2__.clearServiceUrlCache)();\n            }\n        } catch (error) {\n            console.warn('Python service health check failed:', error);\n            serviceAvailable = false;\n            // Clear cached URL to force rediscovery next time\n            (0,_lib_service_discovery__WEBPACK_IMPORTED_MODULE_2__.clearServiceUrlCache)();\n        }\n        // If service is not available, check if tools are explicitly enabled\n        if (!serviceAvailable) {\n            if (mode === 'orchestrator') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Enhanced orchestrator mode is currently unavailable. Please try again later or use simple chat mode.'\n                }, {\n                    status: 503\n                });\n            }\n            // If tools are explicitly enabled, don't fallback - force Python API usage\n            if (config.tools_enabled === true) {\n                console.log('🔧 TOOL FORCING: Python service unavailable but tools explicitly enabled - blocking fallback');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Tools functionality requires the Python service which is currently unavailable. Please try again later or disable tools to use simple chat mode.'\n                }, {\n                    status: 503\n                });\n            }\n            console.log('Python service unavailable, falling back to direct Chutes API (tools disabled)');\n            return fallbackToDirectAPI(messages, stream);\n        }\n        // Get API key from environment\n        const apiKey = \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\";\n        // Get user settings and determine the model to use\n        const userSettings = getUserSettings();\n        let selectedModel = null;\n        if (userSettings && userSettings.preferredModels) {\n            // Map mode to the appropriate model setting\n            if (mode === 'orchestrator') {\n                selectedModel = userSettings.preferredModels.orchestrator;\n            } else {\n                selectedModel = userSettings.preferredModels.enhanced;\n            }\n        }\n        console.log(`Using model: ${selectedModel || 'default'} for mode: ${mode}`);\n        // Forward request to Python service\n        console.log(`Forwarding request to Python service: ${serviceUrl}`);\n        // Create a custom AbortController for dynamic timeout management\n        const abortController = new AbortController();\n        let timeoutId1;\n        // Function to reset timeout on progress updates\n        const resetTimeout = ()=>{\n            if (timeoutId1) clearTimeout(timeoutId1);\n            timeoutId1 = setTimeout(()=>{\n                console.log('Request timeout after no progress updates');\n                abortController.abort();\n            }, 300000); // 5 minutes timeout, reset on each progress update\n        };\n        // Start initial timeout\n        resetTimeout();\n        const response = await fetch(serviceUrl, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                messages,\n                mode,\n                stream,\n                config,\n                apiKey,\n                model: selectedModel // Include selected model from user settings\n            }),\n            signal: abortController.signal\n        });\n        if (!response.ok) {\n            console.error(`Python service error: ${response.status} ${response.statusText}`);\n            // Try fallback for single mode\n            if (mode === 'single') {\n                console.log('Python service failed, falling back to direct API');\n                return fallbackToDirectAPI(messages, stream);\n            }\n            throw new Error(`Python service error: ${response.status}`);\n        }\n        if (stream && response.body) {\n            // Stream response back to client with dynamic timeout reset on progress updates\n            try {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                const readableStream = new ReadableStream({\n                    start (controller) {\n                        function pump() {\n                            return reader.read().then(({ done, value })=>{\n                                if (done) {\n                                    // Clear timeout when stream is done\n                                    if (timeoutId1) clearTimeout(timeoutId1);\n                                    controller.close();\n                                    return;\n                                }\n                                // Decode chunk and reset timeout on any data received\n                                const chunk = decoder.decode(value, {\n                                    stream: true\n                                });\n                                // Reset timeout on any data received (indicates connection is alive)\n                                resetTimeout();\n                                // Log progress updates for debugging\n                                if (chunk.includes('\"progress\"')) {\n                                    console.log('Progress update detected');\n                                }\n                                controller.enqueue(value);\n                                return pump();\n                            }).catch((error)=>{\n                                // Clear timeout on error\n                                if (timeoutId1) clearTimeout(timeoutId1);\n                                controller.error(error);\n                            });\n                        }\n                        return pump();\n                    },\n                    cancel () {\n                        // Clear timeout if stream is cancelled\n                        if (timeoutId1) clearTimeout(timeoutId1);\n                        reader.releaseLock();\n                    }\n                });\n                return new Response(readableStream, {\n                    headers: {\n                        'Content-Type': 'text/plain',\n                        'Cache-Control': 'no-cache',\n                        'Connection': 'keep-alive',\n                        'X-Accel-Buffering': 'no'\n                    }\n                });\n            } catch (streamError) {\n                // Clear timeout on error\n                if (timeoutId1) clearTimeout(timeoutId1);\n                console.error('Error streaming response:', streamError);\n                throw new Error('Failed to stream response from Python service');\n            }\n        } else {\n            // Clear timeout for non-streaming responses\n            if (timeoutId1) clearTimeout(timeoutId1);\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        }\n    } catch (error) {\n        console.error('Enhanced chat API error:', error);\n        // Clear timeout on error\n        if (typeof timeoutId !== 'undefined' && timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        // Try fallback for single mode if we have the request data\n        try {\n            if (messages && mode !== 'orchestrator') {\n                console.log('Error occurred, attempting fallback to direct API');\n                return fallbackToDirectAPI(messages, stream);\n            }\n        } catch (fallbackError) {\n            console.error('Fallback also failed:', fallbackError);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function fallbackToDirectAPI(messages, stream) {\n    console.log('Executing fallback to direct Chutes API');\n    // Get user settings to determine the model to use\n    const userSettings = getUserSettings();\n    let selectedModel = null;\n    if (userSettings && userSettings.preferredModels) {\n        // Use simple mode model for fallback\n        selectedModel = userSettings.preferredModels.simple;\n    }\n    console.log(`Fallback using model: ${selectedModel || 'default'}`);\n    try {\n        if (stream) {\n            // Implement streaming fallback\n            const encoder = new TextEncoder();\n            const readable = new ReadableStream({\n                async start (controller) {\n                    try {\n                        await _lib_api__WEBPACK_IMPORTED_MODULE_1__.APIService.sendMessage(messages, (chunk)=>{\n                            const chunkData = {\n                                choices: [\n                                    {\n                                        delta: {\n                                            content: chunk\n                                        },\n                                        finish_reason: null\n                                    }\n                                ]\n                            };\n                            controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunkData)}\\n\\n`));\n                        }, ()=>{\n                            const finalChunk = {\n                                choices: [\n                                    {\n                                        delta: {},\n                                        finish_reason: 'stop'\n                                    }\n                                ]\n                            };\n                            controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\\n\\n`));\n                            controller.enqueue(encoder.encode('data: [DONE]\\n\\n'));\n                            controller.close();\n                        }, (error)=>{\n                            const errorData = {\n                                error: {\n                                    message: error\n                                }\n                            };\n                            controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\\n\\n`));\n                            controller.close();\n                        }, selectedModel // Pass the selected model from user settings\n                        );\n                    } catch (error) {\n                        console.error('Fallback streaming error:', error);\n                        controller.error(error);\n                    }\n                }\n            });\n            return new Response(readable, {\n                headers: {\n                    'Content-Type': 'text/plain',\n                    'Cache-Control': 'no-cache',\n                    'Connection': 'keep-alive'\n                }\n            });\n        } else {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.APIService.sendNonStreamingMessage(messages, selectedModel);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response\n            });\n        }\n    } catch (error) {\n        console.error('Fallback API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : 'Fallback API failed'\n        }, {\n            status: 500\n        });\n    }\n}\n// Health check endpoint for the enhanced chat service\nasync function GET() {\n    try {\n        // Discover the correct service URL\n        const PYTHON_SERVICE_URL = await (0,_lib_service_discovery__WEBPACK_IMPORTED_MODULE_2__.discoverServiceUrl)();\n        // Check Python service status\n        const healthCheck = await fetch(`${PYTHON_SERVICE_URL}/health`, {\n            method: 'GET',\n            signal: AbortSignal.timeout(5000)\n        });\n        const pythonServiceStatus = healthCheck.ok;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'healthy',\n            python_service: pythonServiceStatus ? 'available' : 'unavailable',\n            python_service_url: PYTHON_SERVICE_URL,\n            fallback: 'available',\n            modes: {\n                single: pythonServiceStatus ? 'enhanced' : 'fallback',\n                orchestrator: pythonServiceStatus ? 'available' : 'unavailable'\n            }\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'partial',\n            python_service: 'unavailable',\n            fallback: 'available',\n            modes: {\n                single: 'fallback',\n                orchestrator: 'unavailable'\n            },\n            error: error instanceof Error ? error.message : 'Unknown error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/enhanced-chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIService: () => (/* binding */ APIService)\n/* harmony export */ });\nconst API_URL = 'https://llm.chutes.ai/v1/chat/completions';\nconst DEFAULT_MODEL = 'Qwen/Qwen3-235B-A22B-Instruct-2507';\nconst MAX_RETRIES = 3;\nconst RETRY_DELAY = 5000; // 5 seconds\nclass APIService {\n    static async sleep(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    static async retryWithBackoff(operation, maxRetries = MAX_RETRIES, baseDelay = RETRY_DELAY) {\n        let lastError;\n        for(let attempt = 0; attempt <= maxRetries; attempt++){\n            try {\n                return await operation();\n            } catch (error) {\n                lastError = error;\n                // Check if it's a 503 error (service overload)\n                if (error instanceof Error && error.message.includes('503')) {\n                    if (attempt < maxRetries) {\n                        const delay = baseDelay * Math.pow(2, attempt); // Exponential backoff\n                        console.log(`Attempt ${attempt + 1} failed with 503 error. Retrying in ${delay}ms...`);\n                        await this.sleep(delay);\n                        continue;\n                    }\n                }\n                // For non-503 errors or final attempt, throw immediately\n                throw error;\n            }\n        }\n        throw lastError;\n    }\n    static getApiKey() {\n        // In a real app, this should be from environment variables\n        // For now, we'll expect it to be set in the browser's environment\n        return \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0;\n    }\n    static async sendMessage(messages, onChunk, onComplete, onError, model) {\n        const apiKey = this.getApiKey();\n        if (!apiKey) {\n            const error = 'API key not found. Please set NEXT_PUBLIC_CHUTES_API_TOKEN environment variable.';\n            console.error(error);\n            onError?.(error);\n            throw new Error(error);\n        }\n        const request = {\n            model: model || DEFAULT_MODEL,\n            messages,\n            stream: true,\n            max_tokens: 1024,\n            temperature: 0.7\n        };\n        console.log('Sending API request:', {\n            url: API_URL,\n            model: model || DEFAULT_MODEL,\n            messageCount: messages.length\n        });\n        try {\n            const response = await this.retryWithBackoff(async ()=>{\n                const response = await fetch(API_URL, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${apiKey}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(request)\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    const error = `API request failed: ${response.status} ${response.statusText} - ${errorText}`;\n                    throw new Error(error);\n                }\n                return response;\n            });\n            console.log('API response status:', response.status, response.statusText);\n            if (!response.body) {\n                const error = 'No response body received';\n                console.error(error);\n                onError?.(error);\n                throw new Error(error);\n            }\n            console.log('Starting stream processing...');\n            return await this.processStreamResponse(response.body, onChunk, onComplete, onError);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n            console.error('API request error:', error);\n            onError?.(errorMessage);\n            throw error;\n        }\n    }\n    static async processStreamResponse(body, onChunk, onComplete, onError) {\n        const reader = body.getReader();\n        const decoder = new TextDecoder();\n        let fullContent = '';\n        let buffer = '';\n        try {\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) {\n                    console.log('Stream completed, full content:', fullContent);\n                    onComplete?.();\n                    break;\n                }\n                // Decode the chunk and add to buffer\n                const chunk = decoder.decode(value, {\n                    stream: true\n                });\n                buffer += chunk;\n                // Process complete lines (split by double newlines for SSE format)\n                const parts = buffer.split('\\n\\n');\n                buffer = parts.pop() || ''; // Keep incomplete part in buffer\n                for (const part of parts){\n                    const lines = part.split('\\n');\n                    for (const line of lines){\n                        const trimmedLine = line.trim();\n                        if (trimmedLine.startsWith('data: ')) {\n                            const data = trimmedLine.slice(6).trim();\n                            if (data === '[DONE]') {\n                                console.log('Received [DONE], completing stream');\n                                onComplete?.();\n                                return fullContent;\n                            }\n                            if (data === '' || data === '{}') {\n                                continue; // Skip empty data lines\n                            }\n                            try {\n                                const parsed = JSON.parse(data);\n                                const content = parsed.choices[0]?.delta?.content;\n                                if (content) {\n                                    fullContent += content;\n                                    onChunk?.(content);\n                                }\n                                // Check for finish_reason\n                                if (parsed.choices[0]?.finish_reason === 'stop') {\n                                    console.log('Received stop signal');\n                                    onComplete?.();\n                                    return fullContent;\n                                }\n                            } catch (parseError) {\n                                // Skip invalid JSON chunks\n                                console.warn('Failed to parse chunk:', data, parseError);\n                            }\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Stream processing error';\n            console.error('API Stream processing error:', error);\n            console.error('API: Calling onError callback with:', errorMessage);\n            onError?.(errorMessage);\n            throw error;\n        } finally{\n            reader.releaseLock();\n        }\n        return fullContent;\n    }\n    static async sendNonStreamingMessage(messages, model) {\n        const apiKey = this.getApiKey();\n        if (!apiKey) {\n            throw new Error('API key not found. Please set NEXT_PUBLIC_CHUTES_API_TOKEN environment variable.');\n        }\n        const request = {\n            model: model || DEFAULT_MODEL,\n            messages,\n            stream: false,\n            max_tokens: 4000,\n            temperature: 0.7\n        };\n        console.log('Sending non-streaming API request:', {\n            url: API_URL,\n            model: model || DEFAULT_MODEL,\n            messageCount: messages.length\n        });\n        try {\n            const response = await this.retryWithBackoff(async ()=>{\n                const response = await fetch(API_URL, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${apiKey}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(request)\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);\n                }\n                return response;\n            });\n            const data = await response.json();\n            return data.choices[0]?.message?.content || '';\n        } catch (error) {\n            console.error('Non-streaming API request failed:', error);\n            throw error;\n        }\n    }\n    static async refineText(text, model) {\n        const refinePrompt = `Please refine and improve the following text to make it clearer, more precise, and better suited for AI interaction. Keep the original intent but enhance clarity, grammar, and structure. Return ONLY the refined text without any additional commentary or explanation:\n\n\"${text}\"\n\nRefined version:`;\n        const messages = [\n            {\n                role: 'user',\n                content: refinePrompt\n            }\n        ];\n        const refinedText = await APIService.sendNonStreamingMessage(messages, model);\n        // Remove leading/trailing quotes and newlines\n        return refinedText.replace(/^[\\n\\r\"]+|[\\n\\r\"]+$/g, '').trim();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/service-discovery.ts":
/*!**************************************!*\
  !*** ./src/lib/service-discovery.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearServiceUrlCache: () => (/* binding */ clearServiceUrlCache),\n/* harmony export */   discoverServiceUrl: () => (/* binding */ discoverServiceUrl),\n/* harmony export */   getCachedServiceUrl: () => (/* binding */ getCachedServiceUrl)\n/* harmony export */ });\n// Service discovery utility for finding the Python backend service\nconst DEFAULT_PYTHON_SERVICE_URL = process.env.PYTHON_SERVICE_URL || 'http://localhost:8000';\n// Cache for discovered service URL\nlet discoveredServiceUrl = null;\nlet lastDiscoveryTime = 0;\nconst CACHE_DURATION = 60000; // 1 minute cache\n// Function to scan ports 8000-8010 to find the running service\nasync function discoverServiceUrl() {\n    // Return cached URL if available and not expired\n    const now = Date.now();\n    if (discoveredServiceUrl && now - lastDiscoveryTime < CACHE_DURATION) {\n        return discoveredServiceUrl;\n    }\n    console.log('Discovering Python service URL...');\n    // First try the default URL\n    try {\n        const response = await fetch(`${DEFAULT_PYTHON_SERVICE_URL}/health`, {\n            method: 'GET',\n            signal: AbortSignal.timeout(3000)\n        });\n        if (response.ok) {\n            discoveredServiceUrl = DEFAULT_PYTHON_SERVICE_URL;\n            lastDiscoveryTime = now;\n            console.log(`Service found at default URL: ${DEFAULT_PYTHON_SERVICE_URL}`);\n            return discoveredServiceUrl;\n        }\n    } catch (error) {\n        console.log(`Default service URL ${DEFAULT_PYTHON_SERVICE_URL} not responding, scanning ports...`);\n    }\n    // Scan ports 8000-8010\n    for(let port = 8000; port <= 8010; port++){\n        const testUrl = `http://localhost:${port}`;\n        try {\n            const response = await fetch(`${testUrl}/health`, {\n                method: 'GET',\n                signal: AbortSignal.timeout(2000)\n            });\n            if (response.ok) {\n                discoveredServiceUrl = testUrl;\n                lastDiscoveryTime = now;\n                console.log(`Service discovered at: ${testUrl}`);\n                return discoveredServiceUrl;\n            }\n        } catch (error) {\n        // Continue to next port\n        }\n    }\n    // If no service found, return default URL\n    console.warn('No service found on ports 8000-8010, using default URL');\n    return DEFAULT_PYTHON_SERVICE_URL;\n}\n// Function to clear the cached service URL (call when service becomes unavailable)\nfunction clearServiceUrlCache() {\n    discoveredServiceUrl = null;\n    lastDiscoveryTime = 0;\n    console.log('Service URL cache cleared');\n}\n// Function to get the cached service URL without discovery\nfunction getCachedServiceUrl() {\n    const now = Date.now();\n    if (discoveredServiceUrl && now - lastDiscoveryTime < CACHE_DURATION) {\n        return discoveredServiceUrl;\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/service-discovery.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhanced-chat%2Froute&page=%2Fapi%2Fenhanced-chat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhanced-chat%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();