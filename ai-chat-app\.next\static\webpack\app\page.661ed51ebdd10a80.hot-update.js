"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Sidebar(param) {\n    let { conversations, currentConversationId, isOpen, onToggle, onNewChat, onSelectConversation, onDeleteConversation, onRenameConversation } = param;\n    _s();\n    const [editingId, setEditingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTitle, setEditTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleStartEdit = (conversation)=>{\n        setEditingId(conversation.id);\n        setEditTitle(conversation.title);\n    };\n    const handleSaveEdit = ()=>{\n        if (editingId && editTitle.trim()) {\n            onRenameConversation(editingId, editTitle.trim());\n        }\n        setEditingId(null);\n        setEditTitle('');\n    };\n    const handleCancelEdit = ()=>{\n        setEditingId(null);\n        setEditTitle('');\n    };\n    const formatDate = (timestamp)=>{\n        const date = new Date(timestamp);\n        const now = new Date();\n        const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n        if (diffInDays === 0) {\n            return 'Today';\n        } else if (diffInDays === 1) {\n            return 'Yesterday';\n        } else if (diffInDays < 7) {\n            return \"\".concat(diffInDays, \" days ago\");\n        } else {\n            return date.toLocaleDateString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-0 top-0 h-full bg-sidebar text-sidebar-foreground z-50 transition-transform duration-300 ease-in-out\", \"w-80 md:w-64\", isOpen ? \"translate-x-0\" : \"-translate-x-full\", \"md:relative md:translate-x-0\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"Nexus\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    className: \"p-2 hover:bg-sidebar-accent rounded-lg md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onNewChat,\n                                className: \"w-full flex items-center gap-3 p-3 bg-sidebar-accent hover:bg-sidebar-accent/80 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"New Chat\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto px-4 pb-4\",\n                            children: conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sidebar-muted text-center py-8\",\n                                children: \"No conversations yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group relative p-3 rounded-lg cursor-pointer transition-colors\", currentConversationId === conversation.id ? \"bg-sidebar-accent\" : \"hover:bg-sidebar-accent/50\"),\n                                        onClick: ()=>onSelectConversation(conversation.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: editingId === conversation.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: editTitle,\n                                                        onChange: (e)=>setEditTitle(e.target.value),\n                                                        onBlur: handleSaveEdit,\n                                                        onKeyDown: (e)=>{\n                                                            if (e.key === 'Enter') handleSaveEdit();\n                                                            if (e.key === 'Escape') handleCancelEdit();\n                                                        },\n                                                        className: \"w-full bg-sidebar-accent text-sidebar-foreground px-2 py-1 rounded text-sm\",\n                                                        autoFocus: true,\n                                                        onClick: (e)=>e.stopPropagation()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-sm truncate\",\n                                                                children: conversation.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-sidebar-muted mt-1\",\n                                                                children: formatDate(conversation.updatedAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 opacity-100 transition-opacity duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                handleStartEdit(conversation);\n                                                            },\n                                                            className: \"p-1 hover:bg-sidebar-accent rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                onDeleteConversation(conversation.id);\n                                                            },\n                                                            className: \"p-1 hover:bg-sidebar-accent rounded text-red-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, conversation.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"fixed top-4 left-4 z-30 p-2 bg-sidebar text-sidebar-foreground rounded-lg md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"e6bBlzAM4+llRiOKTAU/rqKcBJ0=\");\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});